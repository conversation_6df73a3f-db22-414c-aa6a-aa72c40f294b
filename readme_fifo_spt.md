# FIFO-SPT 混合调度算法实现总结



参考：多目标柔性作业车间调度模型及其进化算法研究  王春  6.5.4



## 概述

构建就绪队列：收集所有工件的下一个待处理工序，计算其就绪时间（上一工序完成时间）

FIFO工序选择：从就绪队列中选择就绪时间最早的工序

SPT机器选择：在选定工序的可用机器中，选择加工时间最短的机器（注意：不是最早可用的机器）

状态更新：同步更新机器可用时间、工件进度、工件完成时间，并记录调度结果

循环迭代：重复上述过程直到所有工序完成


本文档总结了 `fifo_spt_scheduler.py` 文件中实现的柔性作业车间调度问题(FJSP)的 FIFO-SPT 混合调度算法。该算法结合了 FIFO（先到先服务）和 SPT（最短加工时间优先）两种经典调度规则的优势。

## 算法原理

### 两阶段决策过程

1. **阶段1 - 工序选择（FIFO规则）**：

   - 从所有就绪的操作中选择最早就绪的操作
   - 确保调度的公平性，避免某些工件长期等待
2. **阶段2 - 机器选择（SPT规则）**：

   - 在选定操作的可用机器集合中选择加工时间最短的机器
   - 在局部范围内优化加工效率

### 核心优势

- **公平性**：FIFO规则确保工件按到达顺序得到处理
- **效率性**：SPT规则在机器选择阶段优化局部加工时间
- **平衡性**：两种规则的结合在公平性和效率性之间取得平衡

## 数据结构设计

### 需要维护的核心 List 数量：**4个主要数据结构**

#### 1. 机器状态跟踪

```python
machine_available_time = [0] * self.num_machines  # 长度为机器数量的数组
```

- **用途**：记录每台机器的最早可用时间
- **作用**：避免机器时间冲突，确保同一台机器不会同时处理多个操作

#### 2. 工件进度跟踪

```python
job_next_operation = [0] * num_jobs           # 长度为工件数量的数组
job_last_completion_time = [0] * num_jobs     # 长度为工件数量的数组
```

- **用途**：
  - `job_next_operation[i]`：跟踪工件i下一个要处理的操作索引
  - `job_last_completion_time[i]`：记录工件i上一个操作的完成时间
- **作用**：确保工序优先级约束，保证同一工件的操作按顺序执行

#### 3. 就绪操作队列

```python
ready_operations = []  # 动态列表，存储(就绪时间, 工件ID, 操作ID)元组
```

- **用途**：存储当前所有可以开始执行的操作
- **作用**：实现FIFO规则的核心数据结构，支持按就绪时间排序

#### 4. 调度结果存储

```python
schedule = []  # 存储调度结果的列表
```

- **用途**：记录每个操作的详细调度信息
- **内容**：工件号、操作号、机器号、开始时间、加工时间、完成时间

## 实现思路分析

### 为什么需要维护多个 List？

#### 1. **约束管理的需要**

- **机器约束**：同一台机器不能同时处理多个操作
- **工序约束**：同一工件的操作必须按顺序执行
- **时间约束**：操作的开始时间受前序操作和机器可用性影响

#### 2. **决策信息的分离**

- **工序选择**：需要知道哪些操作已就绪（ready_operations）
- **机器选择**：需要知道机器的可用时间（machine_available_time）
- **状态更新**：需要跟踪工件进度（job_next_operation, job_last_completion_time）

#### 3. **算法效率的考虑**

- **快速查找**：通过索引直接访问机器和工件状态，时间复杂度O(1)
- **排序优化**：就绪操作列表支持高效排序，实现FIFO规则
- **状态同步**：多个数据结构协同工作，确保系统状态一致性

### 一般实现思路

#### 1. **初始化阶段**

```python
# 初始化所有跟踪数据结构
machine_available_time = [0] * num_machines
job_next_operation = [0] * num_jobs
job_last_completion_time = [0] * num_jobs
schedule = []
```

#### 2. **主调度循环**

```python
while 还有未完成的操作:
    # 步骤1：识别就绪操作
    ready_operations = 收集所有可以开始的操作()
  
    # 步骤2：FIFO工序选择
    selected_operation = 选择最早就绪的操作(ready_operations)
  
    # 步骤3：SPT机器选择
    best_machine = 选择加工时间最短的机器(selected_operation)
  
    # 步骤4：执行调度并更新状态
    更新机器可用时间(best_machine)
    更新工件进度(selected_job)
    记录调度结果(schedule)
```

#### 3. **状态更新策略**

- **原子性更新**：每次调度决策后立即更新所有相关状态
- **一致性检查**：确保机器时间、工件进度、调度记录的一致性
- **约束验证**：在每次更新后验证所有约束条件

## 性能特点

### 时间复杂度

- **单次迭代**：O(n + m)，其中n为工件数，m为机器数
- **总体复杂度**：O(k × (n + m))，其中k为总操作数

### 空间复杂度

- **O(n + m + k)**：分别对应工件状态、机器状态和调度结果存储

### 实际性能

- **MK01基准测试**：在10工件6机器的标准实例上表现良好
- **与经典规则对比**：在公平性和效率性之间取得良好平衡

## 验证机制

算法实现了完整的验证体系：

1. **工序优先级验证**：确保同一工件的操作按顺序执行
2. **机器冲突检测**：验证机器时间分配的正确性
3. **加工时间验证**：检查实际加工时间与标准时间的一致性
4. **性能指标计算**：Makespan、机器利用率、平均完成时间等

## 扩展性设计

- **模块化结构**：调度逻辑、验证机制、性能分析相互独立
- **规则可替换**：支持轻松替换FIFO或SPT规则
- **数据格式标准化**：兼容MK系列标准基准实例
- **对比测试框架**：内置多种经典调度规则的对比测试

## 基于数据结构的算法实现详解

### 算法流程图

```mermaid
flowchart TD
    A[开始] --> B[初始化数据结构]
    B --> C{是否还有未完成操作?}
    C -->|否| Z[结束 - 返回makespan和schedule]
    C -->|是| D[构建就绪操作队列 ready_operations]

    D --> E[遍历所有工件]
    E --> F{工件是否有下一个操作?}
    F -->|是| G[计算操作就绪时间<br/>ready_time = job_last_completion_time job_id]
    G --> H[添加到ready_operations<br/>ready_time job_id op_id]
    F -->|否| I{是否还有工件未检查?}
    H --> I
    I -->|是| E
    I -->|否| J[FIFO规则：按就绪时间排序ready_operations]

    J --> K[选择最早就绪的操作<br/>earliest_ready_time selected_job selected_op]
    K --> L[获取操作的可用机器列表<br/>operation_machines = jobs selected_job selected_op]

    L --> M[SPT规则：遍历可用机器]
    M --> N[计算机器开始时间<br/>start_time = max machine_available_time m earliest_ready_time]
    N --> O{processing_time < best_processing_time?}
    O -->|是| P[更新最佳机器选择<br/>best_machine best_processing_time best_start_time]
    O -->|否| Q{还有机器未检查?}
    P --> Q
    Q -->|是| M
    Q -->|否| R[执行调度决策]

    R --> S[计算完成时间<br/>completion_time = best_start_time + best_processing_time]
    S --> T[更新机器状态<br/>machine_available_time best_machine = completion_time]
    T --> U[更新工件进度<br/>job_last_completion_time selected_job = completion_time<br/>job_next_operation selected_job += 1]
    U --> V[记录调度结果到schedule]
    V --> W[completed_operations += 1]
    W --> C
```

### 数据结构交互详解

#### 1. 机器状态跟踪 (machine_available_time)

**作用机制**：

- **初始化**：所有机器可用时间设为0
- **查询时机**：每次为操作选择机器时查询
- **更新时机**：每次分配操作到机器后更新
- **更新方式**：`machine_available_time[machine_id] = completion_time`

**关键代码片段**：

```python
# 初始化
machine_available_time = [0] * self.num_machines

# 查询：计算操作在该机器上的实际开始时间
start_time = max(machine_available_time[machine_id], earliest_ready_time)

# 更新：操作完成后更新机器可用时间
machine_available_time[best_machine] = completion_time
```

**解决的问题**：

- 防止机器时间冲突
- 确保同一台机器的操作按时间顺序执行
- 支持makespan计算（所有机器中最晚的完成时间）

#### 2. 工件进度跟踪 (job_next_operation & job_last_completion_time)

**job_next_operation 数组**：

- **初始化**：所有工件的下一操作索引设为0
- **查询时机**：每轮循环检查工件是否有待处理操作
- **更新时机**：工件的一个操作完成后
- **更新方式**：`job_next_operation[job_id] += 1`

**job_last_completion_time 数组**：

- **初始化**：所有工件的上次完成时间设为0
- **查询时机**：计算操作的最早就绪时间
- **更新时机**：工件的一个操作完成后
- **更新方式**：`job_last_completion_time[job_id] = completion_time`

**关键代码片段**：

```python
# 初始化
job_next_operation = [0] * num_jobs
job_last_completion_time = [0] * num_jobs

# 查询：检查工件是否还有未完成操作
if job_next_operation[job_id] < len(jobs[job_id]):
    # 计算操作就绪时间
    ready_time = job_last_completion_time[job_id]

# 更新：操作完成后更新工件状态
job_last_completion_time[selected_job] = completion_time
job_next_operation[selected_job] += 1
```

**解决的问题**：

- 确保工序优先级约束（同一工件的操作按顺序执行）
- 计算操作的最早可开始时间
- 跟踪工件的处理进度

#### 3. 就绪操作队列 (ready_operations)

**数据结构**：

- **类型**：列表，存储元组 `(ready_time, job_id, operation_id)`
- **生命周期**：每轮循环重新构建
- **排序规则**：按就绪时间升序，时间相同时按工件ID和操作ID升序

**构建过程**：

```python
ready_operations = []
for job_id in range(num_jobs):
    if job_next_operation[job_id] < len(jobs[job_id]):
        ready_time = job_last_completion_time[job_id]
        ready_operations.append((ready_time, job_id, job_next_operation[job_id]))

# FIFO排序
ready_operations.sort(key=lambda x: (x[0], x[1], x[2]))
```

**FIFO规则实现**：

- 选择就绪时间最早的操作
- 时间相同时按工件ID优先级选择
- 确保调度的公平性和确定性

#### 4. 调度结果存储 (schedule)

**数据结构**：

- **类型**：字典列表，每个字典包含一次调度决策的完整信息
- **内容**：工件号、操作号、机器号、开始时间、加工时间、完成时间

**记录格式**：

```python
schedule.append({
    'job': selected_job + 1,           # 工件编号（1-based）
    'operation': selected_op + 1,      # 操作编号（1-based）
    'machine': best_machine + 1,       # 机器编号（1-based）
    'start_time': best_start_time,     # 开始时间
    'processing_time': best_processing_time,  # 加工时间
    'completion_time': completion_time  # 完成时间
})
```

**用途**：

- 完整记录调度方案
- 支持结果验证和性能分析
- 提供甘特图绘制的数据基础

### 数据结构协同工作机制

#### 循环迭代中的数据流

```
1. 读取阶段：
   job_next_operation → 确定哪些工件有待处理操作
   job_last_completion_time → 计算操作就绪时间
   ↓
2. 决策阶段：
   ready_operations → FIFO工序选择
   machine_available_time → SPT机器选择
   ↓
3. 更新阶段：
   machine_available_time ← 更新机器可用时间
   job_last_completion_time ← 更新工件完成时间
   job_next_operation ← 推进工件进度
   schedule ← 记录调度结果
```

#### 约束保证机制

**时间约束**：

- `start_time = max(machine_available_time[machine_id], ready_time)`
- 确保操作不会在机器忙碌时开始，也不会在前序操作完成前开始

**工序约束**：

- 通过 `job_next_operation` 确保操作按顺序处理
- 通过 `job_last_completion_time` 确保时间依赖关系

**机器约束**：

- 通过 `machine_available_time` 防止机器时间冲突
- 每次分配后立即更新机器状态

### 算法复杂度分析

#### 时间复杂度详解

**单次迭代**：

- 构建就绪操作队列：O(n)，其中n为工件数
- FIFO排序：O(n log n)
- SPT机器选择：O(m)，其中m为平均可用机器数
- 状态更新：O(1)

**总体复杂度**：

- O(k × n log n)，其中k为总操作数
- 在实际应用中，由于n通常较小，性能表现良好

#### 空间复杂度详解

- `machine_available_time`：O(m)
- `job_next_operation` + `job_last_completion_time`：O(n)
- `ready_operations`：O(n)（最坏情况）
- `schedule`：O(k)
- **总计**：O(m + n + k)

## 总结

FIFO-SPT混合调度算法通过维护4个核心数据结构，实现了对柔性作业车间调度问题的有效求解。多个List的设计不仅满足了复杂约束管理的需要，还保证了算法的效率和可维护性。这种设计模式为类似的调度问题提供了良好的参考框架。

### 关键设计原则

1. **状态分离**：不同类型的状态信息用不同的数据结构管理
2. **约束解耦**：机器约束、工序约束、时间约束分别处理
3. **决策分阶段**：工序选择和机器选择分离，降低复杂度
4. **状态同步**：每次决策后立即更新所有相关状态，保证一致性
