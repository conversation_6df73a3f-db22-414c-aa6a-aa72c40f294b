"""
柔性作业车间调度问题(FJSP) - FIFO-SPT混合调度规则

- 工序选择：FIFO规则（先到先服务）
- 机器选择：SPT规则（选择可加工机器集合中加工时间最短的机器）

基于MK01标准基准实例来测试
"""

class FJSPSchedulerFifoSpt:

    def __init__(self):
        """
        初始化调度器
        
        MK01实例配置：
        - 10个工件
        - 6台机器  
        - 每个工件有5-6道工序
        - 每道工序可在多台机器上加工
        """
        self.num_machines = 6  # MK01实例有6台机器
        
    def parse_mk01_data(self):
        """
        解析MK01标准数据格式
        
        MK01数据格式说明：
        每行代表一个工件，格式为：
        [操作数] [机器数1] [机器ID1] [时间1] [机器ID2] [时间2] ... [机器数2] [机器ID3] [时间3] ...
        
        返回:
            list: jobs[i][j] = [(machine_id, processing_time), ...]
                 表示工件i的第j个工序可在哪些机器上加工及对应时间
            
            最终返回三维嵌套的list
            [[[<工件1工序1的可用机器及加工时间>], [], ...], [[工件2工序1的可用机器及加工时间>], [], ...]]

        """
        # MK01  10个工件
        raw_lines = [
            # 工件1：6道工序
            "6 2 1 5 3 4 3 5 3 3 5 2 1 2 3 4 6 2 3 6 5 2 6 1 1 1 3 1 3 6 6 3 6 4 3",
            # 工件2：5道工序
            "5 1 2 6 1 3 1 1 1 2 2 2 6 4 6 3 6 5 2 6 1 1",
            # 工件3：5道工序
            "5 1 2 6 2 3 4 6 2 3 6 5 2 6 1 1 3 3 4 2 6 6 6 2 1 1 5 5",
            # 工件4：5道工序
            "5 3 6 5 2 6 1 1 1 2 6 1 3 1 3 5 3 3 5 2 1 2 3 4 6 2",
            # 工件5：6道工序
            "6 3 5 3 3 5 2 1 3 6 5 2 6 1 1 1 2 6 2 1 5 3 4 2 2 6 4 6 3 3 4 2 6 6 6",
            # 工件6：6道工序
            "6 2 3 4 6 2 1 1 2 3 3 4 2 6 6 6 1 2 6 3 6 5 2 6 1 1 2 1 3 4 2",
            # 工件7：5道工序
            "5 1 6 1 2 1 3 4 2 3 3 4 2 6 6 6 3 2 6 5 1 1 6 1 3 1",
            # 工件8：5道工序
            "5 2 3 4 6 2 3 3 4 2 6 6 6 3 6 5 2 6 1 1 1 2 6 2 2 6 4 6",
            # 工件9：6道工序
            "6 1 6 1 2 1 1 5 5 3 6 6 3 6 4 3 1 1 2 3 3 4 2 6 6 6 2 2 6 4 6",
            # 工件10：6道工序
            "6 2 3 4 6 2 3 3 4 2 6 6 6 3 5 3 3 5 2 1 1 6 1 2 2 6 4 6 2 1 3 4 2"
        ]
        
        # 解析数据结构
        jobs = []
        for _, line in enumerate(raw_lines):
            numbers = list(map(int, line.split()))  # 将字符串分割并转换为整数
            num_operations = numbers[0]  # 该工件的操作数
            operations = []

            idx = 1  # 数据索引指针
            # 解析每道工序
            for _ in range(num_operations):
                num_machines = numbers[idx]  # 该工序可用的机器数
                idx += 1
                machine_options = []

                # 解析每台可用机器及其加工时间
                for _ in range(num_machines):
                    machine_id = numbers[idx] - 1  # 转换为0-based索引（原数据是1-based）
                    processing_time = numbers[idx + 1]  # 在该机器上的加工时间
                    machine_options.append((machine_id, processing_time))
                    idx += 2  # 跳过机器ID和加工时间

                operations.append(machine_options)
            jobs.append(operations)

        return jobs
    
    def fifo_spt_scheduling(self, jobs):
        """
        FIFO-SPT混合调度规则
    
        步骤:
            1. 初始化：设置机器可用时间、工件进度跟踪等数据结构
            2. 循环调度：直到所有操作都被调度完成
               a) 阶段1-工序选择：识别所有准备就绪的操作，按FIFO规则选择最早就绪的
               b) 阶段2-机器选择：在选定操作的可用机器中，按SPT规则选择加工时间最短的
               c) 执行调度：分配机器并更新系统状态
            3. 计算并返回makespan和完整调度方案
        """
        # === 1. 初始化数据结构 ===
        num_jobs = len(jobs)  # 工件总数
        
        # 机器可用时间数组：machine_available_time[i] 表示机器i的最早可用时间
        machine_available_time = [0] * self.num_machines
        
        # 工件进度跟踪：job_next_operation[i] 表示工件i下一个要处理的操作索引
        job_next_operation = [0] * num_jobs
        
        # 工件完成时间：job_last_completion_time[i] 表示工件i上一个操作的完成时间
        job_last_completion_time = [0] * num_jobs
        
        # 调度结果存储
        schedule = []
        total_operations = sum(len(job) for job in jobs)  # 总操作数
        completed_operations = 0  # 已完成操作数
        
        print("=== FIFO-SPT混合调度过程 ===")
        
        # === 2. 主调度循环 ===
        while completed_operations < total_operations:
            # === 2a. 阶段1：FIFO工序选择 ===
            ready_operations = []  # 存储(就绪时间, 工件ID, 操作ID)的元组
            
            # 更新：每个工件下一个要处理的工序号；
            for job_id in range(num_jobs):
                # 检查工件是否还有未完成的操作
                # 判断：第 job_id 个工件下一个要处理的工件序号 < 第 job_id 个工件的总操作数
                if job_next_operation[job_id] < len(jobs[job_id]):
                    # 记录：第 job_id 个工件上一个工序的完工时间=下一个要处理的工序的最早开始时间
                    ready_time = job_last_completion_time[job_id]  # 该操作的最早开始时间
                    ready_operations.append((ready_time, job_id, job_next_operation[job_id]))  # 工件 job_id 的第 job_next_operation[job_id] 个操作已准备就绪/最早可以加工的时间

            
            # 如果没有准备就绪的操作，退出循环（理论上不应该发生）
            if not ready_operations:
                break
            
            # 按FIFO规则选择操作：按就绪时间升序，时间相同时按工件ID和操作ID升序（保证稳定性）
            ready_operations.sort(key=lambda x: (x[0], x[1], x[2]))
            earliest_ready_time, selected_job, selected_op = ready_operations[0]
            
            # === 2b. 阶段2：SPT机器选择 ===
            # 获取选定操作的所有兼容机器及其加工时间
            operation_machines = jobs[selected_job][selected_op]
            
            # 在所有兼容机器中选择加工时间最短的机器
            best_machine = None          # 最佳机器ID
            best_processing_time = float('inf')  # 最短加工时间
            best_start_time = 0          # 对应的开始时间
            
            for machine_id, processing_time in operation_machines:
                # 计算在该机器上的实际开始时间
                start_time = max(machine_available_time[machine_id], earliest_ready_time)
                
                # SPT规则：选择加工时间最短的机器
                if processing_time < best_processing_time:
                    best_processing_time = processing_time
                    best_machine = machine_id
                    best_start_time = start_time
                elif processing_time == best_processing_time:
                    # 加工时间相同时，选择最早可用的机器作为次要条件
                    if start_time < best_start_time:
                        best_machine = machine_id
                        best_start_time = start_time
            
            # === 2c. 执行调度并更新状态 ===
            completion_time = best_start_time + best_processing_time
            
            # 更新机器可用时间
            machine_available_time[best_machine] = completion_time
            
            # 更新工件状态
            job_last_completion_time[selected_job] = completion_time  # 更新工件完成时间
            job_next_operation[selected_job] += 1  # 工件进入下一个操作
            
            # 记录调度结果（转换为1-based索引用于显示）
            schedule.append({
                'job': selected_job + 1,           # 工件编号（1-based）
                'operation': selected_op + 1,      # 操作编号（1-based）
                'machine': best_machine + 1,       # 机器编号（1-based）
                'start_time': best_start_time,     # 开始时间
                'processing_time': best_processing_time,  # 加工时间
                'completion_time': completion_time  # 完成时间
            })
            
            completed_operations += 1
            
            # 显示前10个操作的调度详情（用于调试和验证）
            if completed_operations <= 10:
                print(f"工件{selected_job+1} 工序{selected_op+1}: 机器{best_machine+1}(时长{best_processing_time}), "
                      f"开始{best_start_time}, 完成{completion_time}")
        
        # === 3. 计算最终结果 ===
        makespan = max(machine_available_time)  # 所有机器中最晚的完成时间
        print(f"FIFO-SPT总排产时间: {makespan}\n")
        return makespan, schedule

    def run_fifo_spt_test(self):
        """运行FIFO-SPT混合调度规则测试"""
        jobs = self.parse_mk01_data()

        print("=== MK01 数据验证 ===")
        print(f"工件数量: {len(jobs)}")
        for i, job in enumerate(jobs):
            print(f"工件{i+1}: {len(job)}道工序")
        print()

        # 执行FIFO-SPT调度
        makespan, schedule = self.fifo_spt_scheduling(jobs)

        print(f"\n=== FIFO-SPT调度结果 ===")
        print(f"总完工时间(Makespan): {makespan}")
        print(f"参考信息: MK01已知最优解范围为36-42")

        return makespan, schedule

# 主程序执行
if __name__ == "__main__":
    # FIFO-SPT测试
    scheduler = FJSPSchedulerFifoSpt()
    makespan, schedule = scheduler.run_fifo_spt_test()